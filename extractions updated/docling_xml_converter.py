from docling.document_converter import DocumentConverter
from pathlib import Path

converter = DocumentConverter()
input_dir = Path("xml_files")
output_dir = Path("md_files")
output_dir.mkdir(exist_ok=True)

for xml_path in input_dir.glob("*.xml"):
    result = converter.convert(xml_path)
    if result.status != "SUCCESS":
        print(f"Failed to convert {xml_path.name}")
        continue
    md_path = output_dir / f"{xml_path.stem}.md"
    result.document.save_as_markdown(md_path)  # saves text, tables, references (skipping images)
