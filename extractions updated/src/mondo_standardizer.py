# server.py
import numpy as np
import os
import json
import faiss
import pickle
from typing import Optional, List, Tuple, Union
from mcp.server.fastmcp import FastMCP
from oaklib import get_adapter
from oaklib.datamodels.vocabulary import IS_A
# from collections import Counter, defaultdict  # Unused imports
import openai

from dotenv import load_dotenv
load_dotenv()


# Create an MCP server
mcp = FastMCP("Standardizer")

# Files for persistent storage
cancer_faiss_index_file = 'cancer_faiss.index'
cancer_terms_file = 'cancer_indices.pkl'
cancer_synonyms_file = 'cancer_synonyms.json'

# Set OpenAI API key - replace with your actual API key or use environment variable
openai.api_key = os.environ.get("AZURE_OPENAI_API_KEY")

# Use OpenAI's embedding model
embedding_model = "AZURE_TEXT_EMBEDDING_3_LARGE"  # You can also use "text-embedding-3-large" for higher quality

cancer_mondo_id = 'MONDO:0004992'

# Number of matching standard terms to return
num_output = 10

# Query template to generate query
query_template = "{term} appears in our document in the following context : {context}"
chunk_template = "{term} is defined as {definition}"


# Encode texts using OpenAI's embedding model
def encode(queries: Union[str, List[str]], spans: Optional[Union[Tuple[int,int], List[Tuple[int, int]]]] = None):
    if isinstance(queries, str):
        queries = [queries]
    
    # OpenAI doesn't directly support spans in the same way, so we'll need to adapt our approach
    # If spans are provided, we'll extract the relevant text portions
    if spans is not None:
        if isinstance(spans, tuple):
            spans = [spans]
        
        # Extract the text within the spans
        spanned_queries = []
        for i, query in enumerate(queries):
            if i < len(spans):
                span = spans[i]
                # Extract the text within the span
                spanned_queries.append(query[span[0]:span[1]])
            else:
                spanned_queries.append(query)
        
        # Use the spanned queries instead
        queries = spanned_queries
    
    # Process in batches to avoid API rate limits
    embeddings = []
    batch_size = 8
    
    for batch_i in range(0, len(queries), batch_size):
        batch = queries[batch_i:batch_i+batch_size]
        
        # Call OpenAI API to get embeddings
        response = openai.Embedding.create(
            model=embedding_model,
            input=batch
        )
        
        # Extract embeddings from response
        for i in range(len(batch)):
            embeddings.append(response["data"][i]["embedding"])
    
    return np.array(embeddings)


def save_terms(documents: list[str], terms: list[str], spans: Optional[List[Tuple[int, int]]] = None, faiss_index_file: Optional[str] = cancer_faiss_index_file, terms_file: Optional[str] = cancer_terms_file):
    # Compute embeddings
    embeddings = encode(queries=documents, spans=spans)

    # Build and save FAISS index
    dimension = embeddings.shape[1]
    index = faiss.IndexFlatL2(dimension)
    index.add(embeddings)

    if not os.path.exists(faiss_index_file):
        faiss.write_index(index, faiss_index_file)

    if not os.path.exists(terms_file):
        with open(terms_file, "wb") as f:
            pickle.dump(terms, f)

def get_mondo_descendants(curie: str, adapter) -> list[tuple[str, str]]:
    all_descendants_data = [(adapter.label(child), child)
                for child in adapter.descendants([curie], predicates=[IS_A])
                if child != curie]

    return all_descendants_data

def save_synonyms(cancers, adapter):
    synonyms_dict = {}
    for term, curie in cancers:
        synonyms = adapter.entity_metadata_map(curie).get('oio:hasExactSynonym')
        if synonyms is None:
          synonyms = []
        synonyms.append(term)
        for synonym in synonyms:
            if synonym not in synonyms_dict:
                synonyms_dict[synonym] = term

    if not os.path.exists(cancer_synonyms_file):
        with open(cancer_synonyms_file, 'w') as f:
            json.dump(synonyms_dict, f)

def get_docs_and_terms(cancers, adapter):
    documents = []
    terms = []
    spans = []

    for term, curie in cancers:
        terms.append(term)
        document = chunk_template.format(term=term, definition=adapter.definition(curie))
        documents.append(document)

        #calculate span
        term_position_start = document.find(term)
        term_position_end = term_position_start + len(term)
        span = (term_position_start, term_position_end)
        spans.append((span))

    return documents, terms, spans

def fetch_and_save_cancer_terms():
    # Mondo adapter
    adapter = get_adapter(f"sqlite:obo:mondo")

    cancers = get_mondo_descendants(cancer_mondo_id, adapter)

    save_synonyms(cancers, adapter)

    documents, terms, spans = get_docs_and_terms(cancers, adapter)

    save_terms(documents, terms, spans, cancer_faiss_index_file, cancer_terms_file)


def standardize(term: str, context: Optional[str] = None) -> List[List]:
    faiss_index_file = cancer_faiss_index_file
    terms_file = cancer_terms_file
    synonyms_file = cancer_synonyms_file

    # Load synonyms dict
    with open(synonyms_file, 'r') as f:
        synonyms_dict = json.load(f)

    if term in synonyms_dict:
        return [synonyms_dict[term]]

    # Load index and documents
    index = faiss.read_index(faiss_index_file)
    with open(terms_file, "rb") as f:
        terms = pickle.load(f)

    # Query embedding
    if context is None:
        query = term
        query_embedding = encode(query)
    else:
        query = query_template.format(term=term, context=context)
        term_position_start = query.find(term)
        term_position_end = term_position_start + len(term)
        span = (term_position_start, term_position_end)
        query_embedding = encode(query, span)

    # Search
    _, indices = index.search(query_embedding, num_output)

    results = [terms[i] for i in indices[0]]

    return results


def extract_cancer_terms_from_text(raw_text: str, synonyms_dict: dict) -> List[str]:
    """
    Extracts candidate cancer terms from raw text by matching against known synonyms.
    Returns a list of unique terms found in the text.
    """
    found_terms = set()
    lowered_text = raw_text.lower()
    for synonym in synonyms_dict.keys():
        if synonym.lower() in lowered_text:
            found_terms.add(synonym)
    return list(found_terms)


def standardize_text(raw_text: str) -> dict:
    """
    Given raw text, extract all candidate cancer terms and return their standardized forms.
    Returns a dict mapping found terms to their standardization results.
    """
    synonyms_file = cancer_synonyms_file
    faiss_index_file = cancer_faiss_index_file
    terms_file = cancer_terms_file

    # Ensure index and synonyms exist
    if not (os.path.exists(faiss_index_file) and os.path.exists(terms_file) and os.path.exists(synonyms_file)):
        fetch_and_save_cancer_terms()

    # Load synonyms dict
    with open(synonyms_file, 'r') as f:
        synonyms_dict = json.load(f)

    found_terms = extract_cancer_terms_from_text(raw_text, synonyms_dict)
    results = {}
    for term in found_terms:
        results[term] = standardize(term)
    return results


def standardize_cancer_terms_in_text(raw_text: str) -> str:
    """
    Given raw text, find cancer terms and replace them with their standardized forms.
    Returns the text with cancer terms replaced by their most likely standardized equivalents.
    If the required files don't exist, returns the original text unchanged.
    """
    synonyms_file = cancer_synonyms_file
    faiss_index_file = cancer_faiss_index_file
    terms_file = cancer_terms_file

    # Check if synonyms file exists (minimum requirement)
    if not os.path.exists(synonyms_file):
        print(f"Warning: Cancer synonyms file {synonyms_file} not found. Returning original text.")
        return raw_text

    # Load synonyms dict
    try:
        with open(synonyms_file, 'r') as f:
            synonyms_dict = json.load(f)
    except Exception as e:
        print(f"Warning: Could not load synonyms file: {e}. Returning original text.")
        return raw_text

    # Find cancer terms in the text using synonyms only
    found_terms = extract_cancer_terms_from_text(raw_text, synonyms_dict)

    # Create a copy of the text to modify
    standardized_text = raw_text

    # If FAISS index and terms file exist, use full standardization
    if os.path.exists(faiss_index_file) and os.path.exists(terms_file):
        # Replace each found term with its standardized form
        for term in found_terms:
            try:
                standardized_forms = standardize(term)
                if standardized_forms:
                    # Use the first (most likely) standardized form
                    best_standardized_form = standardized_forms[0]
                    # Replace all occurrences of the term (case-insensitive)
                    import re
                    pattern = re.compile(re.escape(term), re.IGNORECASE)
                    standardized_text = pattern.sub(best_standardized_form, standardized_text)
            except Exception as e:
                print(f"Warning: Could not standardize term '{term}': {e}")
                continue
    else:
        # Fallback: use synonyms dictionary for direct mapping
        import re
        for term in found_terms:
            if term in synonyms_dict:
                standardized_form = synonyms_dict[term]
                pattern = re.compile(re.escape(term), re.IGNORECASE)
                standardized_text = pattern.sub(standardized_form, standardized_text)

    return standardized_text


@mcp.tool()
def get_standard_cancer_type_subtype(cancer_type: str, cancer_type_context: Optional[str] = None, cancer_subtype: Optional[str] = None, cancer_subtype_context: Optional[str] = None) -> Union[List[str], Tuple[List[str],List[str]]]:
    """
    Get list of top standard cancer types and top 5 cancer sub-types based on given cancer type and subtype.

    Parameters:
    cancer_type str: The cancer type to provide matching standard cancer type for.
    cancer_type_context str: The context of the cancer type.
    cancer_subtype Optional[str]: The cancer subtype to provide matching standard cancer subtype for.
    cancer_subtype_context Optional[str]: The context of the cancer subtype.

    Returns:
    List[str]: A list of top matching standard cancer types.
    List[str]: A list of top matching standard cancer subtypes if cancer_subtype is provided.
    """

    if not (os.path.exists(cancer_faiss_index_file) and os.path.exists(cancer_terms_file) and os.path.exists(cancer_synonyms_file)):
        fetch_and_save_cancer_terms()

    if cancer_subtype is None:
        return standardize(cancer_type, cancer_type_context)
    else:
        return standardize(cancer_type, cancer_type_context), standardize(cancer_subtype, cancer_subtype_context)

if __name__ == "__main__":
    mcp.run()

