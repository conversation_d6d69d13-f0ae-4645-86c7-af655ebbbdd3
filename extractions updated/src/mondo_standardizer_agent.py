"""
MCP-based Agent for cancer term standardization using Mondo ontology.
This module provides an Agent that integrates with the mondo_standardizer MCP server.
"""

import os
import logging
from typing import Optional, Dict, Any
from dataclasses import dataclass

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider
from pydantic_ai.tools import RunContext
from openai import AsyncAzureOpenAI
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

@dataclass(frozen=True)
class SharedContext:
    """Immutable core data for a sample that can be safely shared across parallel nodes.
    
    Attributes:
        raw_text (str): The raw text being processed
    """
    raw_text: str 
    
    def with_updates(self, **kwargs) -> "SharedContext":
        """Create a new instance with updated values."""
        return SharedContext(
            raw_text=kwargs.get('raw_text', self.raw_text)
        )

class MondoStandardizerClient:
    """Client for interacting with the Mondo standardizer MCP server."""
    
    def __init__(self):
        self.server_params = StdioServerParameters(
            command="python",
            args=["src/mondo_standardizer.py"],
            env=None,
        )
        self.logger = logging.getLogger(__name__)
    
    async def get_standard_cancer_types(
        self, 
        cancer_type: str, 
        cancer_type_context: Optional[str] = None,
        cancer_subtype: Optional[str] = None,
        cancer_subtype_context: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get standardized cancer types using the MCP server.
        
        Args:
            cancer_type: The cancer type to standardize
            cancer_type_context: Context for the cancer type
            cancer_subtype: Optional cancer subtype
            cancer_subtype_context: Context for the cancer subtype
            
        Returns:
            Dictionary containing standardized cancer types and subtypes
        """
        try:
            async with stdio_client(self.server_params) as (read, write):
                async with ClientSession(read, write) as session:
                    await session.initialize()
                    
                    # Call the standardization tool
                    result = await session.call_tool(
                        "get_standard_cancer_type_subtype",
                        {
                            "cancer_type": cancer_type,
                            "cancer_type_context": cancer_type_context,
                            "cancer_subtype": cancer_subtype,
                            "cancer_subtype_context": cancer_subtype_context
                        }
                    )
                    
                    return {
                        "cancer_type": cancer_type,
                        "standardized_types": result.content,
                        "success": True
                    }
                    
        except Exception as e:
            self.logger.error(f"Error calling MCP server: {e}")
            return {
                "cancer_type": cancer_type,
                "standardized_types": [cancer_type],  # Fallback to original
                "success": False,
                "error": str(e)
            }

# Initialize Azure OpenAI client
client = AsyncAzureOpenAI(
    azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
    azure_deployment='gpt-4.1',
    api_key=os.getenv("AZURE_OPENAI_API_KEY"),
    api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
)

# Initialize the OpenAI model with Azure provider
model = OpenAIModel(model_name='gpt-4.1', provider=OpenAIProvider(openai_client=client))
model_settings = {'temperature': 0.0, 'seed': 777}

# Create the MCP client instance
mondo_client = MondoStandardizerClient()

# Initialize the Agent with the model extraction system prompt and configuration
mondo_standardizer_agent = Agent(
    model=model,
    system_prompt=open("prompts/model_extraction_system_prompt.md").read(),
    retries=3,
    result_retries=3,
    model_settings=model_settings,
    deps_type=SharedContext
)

@mondo_standardizer_agent.tool
async def standardize_cancer_term(
    _ctx: RunContext[SharedContext],
    cancer_type: str,
    cancer_type_context: Optional[str] = None
) -> str:
    """
    Standardize a cancer term using the Mondo ontology.
    
    Args:
        cancer_type: The cancer type to standardize
        cancer_type_context: Optional context for better standardization
        
    Returns:
        The standardized cancer term
    """
    result = await mondo_client.get_standard_cancer_types(
        cancer_type=cancer_type,
        cancer_type_context=cancer_type_context
    )
    
    if result["success"] and result["standardized_types"]:
        # Return the first (most likely) standardized form
        standardized = result["standardized_types"][0]
        return f"Standardized '{cancer_type}' to '{standardized}'"
    else:
        return f"Could not standardize '{cancer_type}', keeping original term"

@mondo_standardizer_agent.tool
async def standardize_cancer_type_and_subtype(
    _ctx: RunContext[SharedContext],
    cancer_type: str,
    cancer_subtype: str,
    cancer_type_context: Optional[str] = None,
    cancer_subtype_context: Optional[str] = None
) -> str:
    """
    Standardize both cancer type and subtype using the Mondo ontology.
    
    Args:
        cancer_type: The cancer type to standardize
        cancer_subtype: The cancer subtype to standardize
        cancer_type_context: Optional context for cancer type
        cancer_subtype_context: Optional context for cancer subtype
        
    Returns:
        Information about both standardized terms
    """
    result = await mondo_client.get_standard_cancer_types(
        cancer_type=cancer_type,
        cancer_type_context=cancer_type_context,
        cancer_subtype=cancer_subtype,
        cancer_subtype_context=cancer_subtype_context
    )
    
    if result["success"] and result["standardized_types"]:
        if isinstance(result["standardized_types"], tuple) and len(result["standardized_types"]) == 2:
            std_type, std_subtype = result["standardized_types"]
            return f"Standardized type '{cancer_type}' to '{std_type[0] if std_type else cancer_type}' and subtype '{cancer_subtype}' to '{std_subtype[0] if std_subtype else cancer_subtype}'"
        else:
            # Only cancer type was standardized
            std_type = result["standardized_types"][0] if result["standardized_types"] else cancer_type
            return f"Standardized type '{cancer_type}' to '{std_type}', subtype '{cancer_subtype}' unchanged"
    else:
        return f"Could not standardize cancer terms, keeping original: type '{cancer_type}', subtype '{cancer_subtype}'"

# Export the agent and client for use in other modules
__all__ = ['mondo_standardizer_agent', 'mondo_client', 'SharedContext']
