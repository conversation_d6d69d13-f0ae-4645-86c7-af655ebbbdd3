import json
import os
from bs4 import BeautifulSoup

def create_labelstudio_preannotations(input_json_path, output_json_path, html_path=None):
    """
    Creates Label Studio pre-annotations without indices, letting Label Studio
    find the text and calculate the indices automatically.
    
    Args:
        input_json_path: Path to your JSON with citations
        output_json_path: Path to save the Label Studio pre-annotations
        html_path: Optional path to HTML file (for verification)
    """
    # Load your JSON data
    with open(input_json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Create Label Studio pre-annotation structure
    ls_data = {
        "data": {
            "html_url": f"/data/local-files/?d=adc_html/{os.path.basename(html_path)}" if html_path else ""
        },
        "predictions": [
            {
                "model_version": "auto-indices",
                "result": []
            }
        ]
    }
    
    # Optional: Load HTML to verify text exists
    html_content = None
    if html_path:
        with open(html_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        soup = BeautifulSoup(html_content, 'html.parser')
        html_text = soup.get_text()
    
    # Extract citations from your data structure
    def extract_citations(obj, parent_path=None):
        if isinstance(obj, dict):
            # Check if this is a citation object
            if all(k in obj for k in ["citation", "verbatim_value"]):
                citation_text = obj["citation"]
                verbatim = obj["verbatim_value"]
                
                # Skip empty or "Not specified" citations
                if not citation_text or verbatim == "Not specified":
                    return
                
                # Get the field name from the parent path
                if parent_path:
                    field_name = parent_path.split(".")[-1]
                    
                    # Skip if citation text doesn't exist in HTML (if HTML is provided)
                    if html_content and citation_text not in html_text:
                        print(f"Warning: Citation text not found in HTML: {citation_text[:50]}...")
                        return
                    
                    # Map field name to taxonomy
                    taxonomy = []
                    if field_name in ["adc_name", "antibody_name", "clonality", "species", "isotype", 
                                     "payload_name", "payload_target", "linker_name", "linker_type", 
                                     "antigen_name"]:
                        taxonomy = [["ADC"], ["ADC", field_name]]
                    elif field_name in ["model_description", "model_type", "cancer_type", "cancer_subtype"]:
                        taxonomy = [["Model"], ["Model", field_name]]
                    elif field_name in ["endpoint_type", "endpoint_name", "endpoint_quantitative_value",
                                       "endpoint_qualitative_value", "endpoint_timepoint", "endpoint_concentration"]:
                        taxonomy = [["Endpoints"], ["Endpoints", field_name]]
                    
                    if taxonomy:
                        # Add to predictions WITHOUT specifying indices
                        ls_data["predictions"][0]["result"].append({
                            "id": f"{field_name}_{len(ls_data['predictions'][0]['result'])}",
                            "from_name": "label",
                            "to_name": "text",
                            "type": "taxonomy",
                            "value": {
                                "text": citation_text,  # Only specify the text
                                "taxonomy": taxonomy
                            }
                        })
            
            # Process nested objects
            for key, value in obj.items():
                new_path = f"{parent_path}.{key}" if parent_path else key
                extract_citations(value, new_path)
        elif isinstance(obj, list):
            # Process list items
            for i, item in enumerate(obj):
                new_path = f"{parent_path}[{i}]" if parent_path else f"[{i}]"
                extract_citations(item, new_path)
    
    # Process experiment results
    if "experiment_results" in data:
        extract_citations(data["experiment_results"])
    else:
        extract_citations(data)
    
    # Save Label Studio pre-annotations
    with open(output_json_path, 'w', encoding='utf-8') as f:
        json.dump(ls_data, f, indent=2)
    
    print(f"Created Label Studio pre-annotations with {len(ls_data['predictions'][0]['result'])} items")
    print(f"Saved to {output_json_path}")

# Example usage
if __name__ == "__main__":
    create_labelstudio_preannotations(
        "indices_results.json",
        "labelstudio_preannotations.json",
        "../adc_html/w2119870604.html"
    )