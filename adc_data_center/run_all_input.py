import os
import glob
import subprocess

# Get all .md files in ./output/
md_files = glob.glob(os.path.join('markdown_batch_5', '*.md')) ## change input source

if not md_files:
    print('No .md files found in ./output/')
    exit(0)

for md_file in md_files:
    print(f'Processing {md_file}...')
    cmd = [
        'python',
        'src/extraction_pipeline_parallel_strurcture.py',
        '--input-file',
        md_file,
        '--output-dir', ## change output source
        'markdown_batch_5_results'
    ]
    result = subprocess.run(cmd)
    if result.returncode != 0:
        print(f'Error processing {md_file}, exited with code {result.returncode}')
        break
print('Done.')
