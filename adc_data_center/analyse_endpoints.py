import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import os
import argparse
from collections import Counter

def analyze_endpoints(excel_file):
    """
    Analyze the endpoints sheet in the Excel file and generate frequency distributions.
    """
    print(f"Reading Excel file: {excel_file}")
    
    try:
        # Read the Endpoints sheet
        endpoints_df = pd.read_excel(excel_file, sheet_name='Endpoints')
        print(f"Successfully loaded Endpoints sheet with {len(endpoints_df)} rows")
        
        # Check if required columns exist
        required_columns = ['endpoint_name', 'id', 'source_file']
        missing_columns = [col for col in required_columns if col not in endpoints_df.columns]
        
        # If 'id' or 'source_file' is missing, we'll use whichever is available for paper identification
        paper_id_col = None
        if 'id' in endpoints_df.columns:
            paper_id_col = 'id'
        elif 'source_file' in endpoints_df.columns:
            paper_id_col = 'source_file'
            
        if 'endpoint_name' not in endpoints_df.columns:
            print("ERROR: 'endpoint_name' column not found in the Endpoints sheet")
            return
        
        if paper_id_col is None:
            print("WARNING: Neither 'id' nor 'source_file' column found. Using row index as paper identifier.")
            endpoints_df['paper_id'] = range(len(endpoints_df))
            paper_id_col = 'paper_id'
            
        # Clean endpoint names (remove extra whitespace, convert to lowercase for consistency)
        endpoints_df['endpoint_name'] = endpoints_df['endpoint_name'].astype(str).str.strip().str.lower()
        
        # Count overall endpoint frequency
        print("\n=== Overall Endpoint Frequency ===")
        endpoint_counts = Counter(endpoints_df['endpoint_name'])
        
        # Sort by frequency (most common first)
        sorted_endpoints = endpoint_counts.most_common()
        
        # Display the frequency distribution
        print(f"Found {len(sorted_endpoints)} unique endpoints")
        print("\nTop 20 endpoints by frequency:")
        for endpoint, count in sorted_endpoints[:20]:
            print(f"  {endpoint}: {count}")
            
        # Count endpoints per paper
        print("\n=== Endpoints per Paper ===")
        endpoints_by_paper = endpoints_df.groupby([paper_id_col, 'endpoint_name']).size().reset_index(name='count')
        papers_count = endpoints_df[paper_id_col].nunique()
        print(f"Found {papers_count} unique papers")
        
        # Count how many papers each endpoint appears in
        papers_per_endpoint = endpoints_by_paper.groupby('endpoint_name').size().reset_index(name='papers_count')
        papers_per_endpoint = papers_per_endpoint.sort_values('papers_count', ascending=False)
        
        print("\nTop 20 endpoints by number of papers:")
        for _, row in papers_per_endpoint.head(20).iterrows():
            endpoint = row['endpoint_name']
            papers = row['papers_count']
            percentage = (papers / papers_count) * 100
            print(f"  {endpoint}: {papers} papers ({percentage:.1f}%)")
            
        # Generate visualizations
        print("\nGenerating visualizations...")
        
        # Create a directory for outputs if it doesn't exist
        os.makedirs('endpoint_analysis', exist_ok=True)
        
        # Plot overall endpoint frequency (top 20)
        plt.figure(figsize=(12, 8))
        top_endpoints = pd.DataFrame(sorted_endpoints[:20], columns=['endpoint', 'count'])
        sns.barplot(x='count', y='endpoint', data=top_endpoints)
        plt.title('Top 20 Endpoints by Frequency')
        plt.tight_layout()
        plt.savefig('endpoint_analysis/top_endpoints_frequency.png')
        
        # Plot papers per endpoint (top 20)
        plt.figure(figsize=(12, 8))
        sns.barplot(x='papers_count', y='endpoint_name', data=papers_per_endpoint.head(20))
        plt.title('Top 20 Endpoints by Number of Papers')
        plt.tight_layout()
        plt.savefig('endpoint_analysis/top_endpoints_by_papers.png')
        
        # Save the frequency distributions to Excel
        print("\nSaving frequency distributions to Excel...")
        with pd.ExcelWriter('endpoint_analysis/endpoint_frequency_analysis.xlsx') as writer:
            # Overall frequency
            pd.DataFrame(sorted_endpoints, columns=['endpoint', 'count']).to_excel(
                writer, sheet_name='Overall Frequency', index=False)
            
            # Papers per endpoint
            papers_per_endpoint.to_excel(writer, sheet_name='Papers per Endpoint', index=False)
            
            # Endpoints per paper
            pivot_table = endpoints_df.pivot_table(
                index=paper_id_col, 
                columns='endpoint_name', 
                values='endpoint_name',
                aggfunc='count',
                fill_value=0
            )
            pivot_table.to_excel(writer, sheet_name='Endpoints per Paper')
        
        print("\nAnalysis complete! Results saved to 'endpoint_analysis' directory.")
        
    except Exception as e:
        import traceback
        print(f"Error analyzing endpoints: {str(e)}")
        print(traceback.format_exc())

def main():
    parser = argparse.ArgumentParser(description="Analyze endpoints in combined Excel file")
    parser.add_argument("--file", type=str, required=True, 
                        help="Path to the combined Excel file")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.file):
        print(f"Error: File not found: {args.file}")
        return
        
    analyze_endpoints(args.file)

if __name__ == "__main__":
    main()