#!/usr/bin/env python3
"""
Script to analyze frequency distributions for each column in the "adc" sheet
of the combined results Excel file.
"""

import pandas as pd
import numpy as np
from collections import Counter
from pathlib import Path

def analyze_adc_sheet_frequencies(excel_file):
    """Analyze frequency distributions for all columns in the adc sheet"""
    
    print(f"Loading Excel file: {excel_file}")
    
    # Load the Excel file and examine sheets
    try:
        excel_data = pd.ExcelFile(excel_file)
        print(f"Available sheets: {excel_data.sheet_names}")
        
        # Load the ADCs sheet (check for both 'adc' and 'ADCs')
        sheet_name = None
        if 'adc' in excel_data.sheet_names:
            sheet_name = 'adc'
        elif 'ADCs' in excel_data.sheet_names:
            sheet_name = 'ADCs'
        else:
            print("Error: Neither 'adc' nor 'ADCs' sheet found in the Excel file!")
            return None

        df_adc = pd.read_excel(excel_file, sheet_name=sheet_name)
        print(f"Using sheet: {sheet_name}")
        print(f"\nADC sheet loaded successfully!")
        print(f"Shape: {df_adc.shape}")
        print(f"Columns: {list(df_adc.columns)}")
        
        return df_adc
        
    except Exception as e:
        print(f"Error loading Excel file: {e}")
        return None

def create_frequency_distributions(df):
    """Create frequency distributions for each column"""
    
    frequency_data = {}
    
    print(f"\n{'='*80}")
    print("FREQUENCY DISTRIBUTION ANALYSIS")
    print(f"{'='*80}")
    
    for column in df.columns:
        print(f"\n**Column Name: [{column}]**")
        
        # Get value counts, including NaN values
        value_counts = df[column].value_counts(dropna=False)
        
        # Convert to dictionary for easier handling
        freq_dict = {}
        for value, count in value_counts.items():
            if pd.isna(value):
                freq_dict['[NULL/Empty]'] = count
            else:
                freq_dict[str(value)] = count
        
        # Store for Excel output
        frequency_data[column] = freq_dict
        
        # Print frequency distribution
        if len(freq_dict) == 0:
            print("- No data found")
        else:
            for value, count in sorted(freq_dict.items(), key=lambda x: x[1], reverse=True):
                print(f"- {value}: {count}")
        
        print(f"Total unique values: {len(freq_dict)}")
        print(f"Total records: {sum(freq_dict.values())}")
    
    return frequency_data

def create_frequency_excel(frequency_data, output_file):
    """Create an Excel file with frequency distributions"""
    
    print(f"\nCreating frequency analysis Excel file: {output_file}")
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        
        # Create summary sheet
        summary_data = []
        for column, freq_dict in frequency_data.items():
            summary_data.append({
                'Column': column,
                'Unique_Values': len(freq_dict),
                'Total_Records': sum(freq_dict.values()),
                'Most_Common_Value': max(freq_dict.items(), key=lambda x: x[1])[0] if freq_dict else 'N/A',
                'Most_Common_Count': max(freq_dict.values()) if freq_dict else 0
            })
        
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='Summary', index=False)
        
        # Create individual sheets for each column
        for column, freq_dict in frequency_data.items():
            # Create DataFrame for this column's frequency data
            freq_df = pd.DataFrame([
                {'Value': value, 'Count': count, 'Percentage': (count / sum(freq_dict.values()) * 100) if freq_dict else 0}
                for value, count in sorted(freq_dict.items(), key=lambda x: x[1], reverse=True)
            ])
            
            # Clean sheet name (Excel sheet names have restrictions)
            sheet_name = column.replace('/', '_').replace('[', '').replace(']', '')[:31]
            freq_df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        # Create a combined overview sheet
        overview_data = []
        for column, freq_dict in frequency_data.items():
            for value, count in freq_dict.items():
                overview_data.append({
                    'Column': column,
                    'Value': value,
                    'Count': count,
                    'Percentage': (count / sum(freq_dict.values()) * 100) if freq_dict else 0
                })
        
        overview_df = pd.DataFrame(overview_data)
        overview_df.to_excel(writer, sheet_name='All_Frequencies', index=False)
    
    print(f"✅ Frequency analysis saved to: {output_file}")

def main():
    """Main analysis function"""
    
    # Look for the combined results file
    excel_files = ['combined_results.xlsx', 'adc_extraction_results.xlsx']
    
    input_file = None
    for file in excel_files:
        if Path(file).exists():
            input_file = file
            break
    
    if not input_file:
        print("Error: No combined results Excel file found!")
        print("Looking for: combined_results.xlsx or adc_extraction_results.xlsx")
        return
    
    # Load and analyze the adc sheet
    df_adc = analyze_adc_sheet_frequencies(input_file)
    
    if df_adc is None:
        return
    
    # Create frequency distributions
    frequency_data = create_frequency_distributions(df_adc)
    
    # Create output Excel file
    output_file = 'adc_frequency_analysis.xlsx'
    create_frequency_excel(frequency_data, output_file)
    
    print(f"\n{'='*80}")
    print("ANALYSIS COMPLETE")
    print(f"{'='*80}")
    print(f"Input file: {input_file}")
    print(f"Output file: {output_file}")
    print(f"Total columns analyzed: {len(frequency_data)}")
    print(f"Total records in adc sheet: {len(df_adc)}")

if __name__ == "__main__":
    main()
