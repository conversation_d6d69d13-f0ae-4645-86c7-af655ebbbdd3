#!/usr/bin/env python3
"""
Test script to process a single markdown file to verify the pipeline works.
"""

import subprocess
import sys
from pathlib import Path

def test_single_file():
    """Test processing a single markdown file"""
    
    # Use the first file from batch 1
    test_file = "markdown_batch_1/W1491690127.md"
    
    if not Path(test_file).exists():
        print(f"Test file {test_file} not found!")
        return False
    
    print(f"Testing pipeline with file: {test_file}")
    
    # Create test output directory
    output_dir = "test_output"
    Path(output_dir).mkdir(exist_ok=True)
    
    cmd = [
        'python',
        'src/extraction_pipeline_parallel_strurcture.py',
        '--input-file',
        test_file,
        '--output-dir',
        output_dir
    ]
    
    print(f"Running command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ Test successful!")
            print("STDOUT:", result.stdout[-500:] if result.stdout else "No output")
            
            # Check if output files were created
            output_files = list(Path(output_dir).glob('*'))
            print(f"Output files created: {len(output_files)}")
            for f in output_files:
                print(f"  - {f}")
            
            return True
        else:
            print(f"❌ Test failed with exit code: {result.returncode}")
            print("STDERR:", result.stderr)
            print("STDOUT:", result.stdout)
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Test timed out (>5 minutes)")
        return False
    except Exception as e:
        print(f"❌ Exception during test: {e}")
        return False

if __name__ == "__main__":
    success = test_single_file()
    sys.exit(0 if success else 1)
