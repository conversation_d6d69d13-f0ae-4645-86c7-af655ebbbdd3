#!/usr/bin/env python3
"""
Script to process markdown files in batches using the extraction pipeline.
This script processes each batch directory sequentially.
"""

import os
import glob
import subprocess
import sys
from pathlib import Path

def process_batch(batch_dir, batch_num):
    """Process a single batch directory"""
    print(f"\n{'='*60}")
    print(f"PROCESSING BATCH {batch_num}: {batch_dir}")
    print(f"{'='*60}")
    
    # Get all .md files in the batch directory
    md_files = glob.glob(os.path.join(batch_dir, '*.md'))
    
    if not md_files:
        print(f'No .md files found in {batch_dir}')
        return False
    
    print(f"Found {len(md_files)} markdown files to process")
    
    # Create output directory for this batch
    output_dir = f'batch_{batch_num}_results'
    Path(output_dir).mkdir(exist_ok=True)
    
    processed_count = 0
    failed_count = 0
    
    for md_file in md_files:
        print(f'\nProcessing {os.path.basename(md_file)}...')
        
        cmd = [
            'python',
            'src/extraction_pipeline_parallel_strurcture.py',
            '--input-file',
            md_file,
            '--output-dir',
            output_dir
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)  # 5 minute timeout per file
            
            if result.returncode == 0:
                processed_count += 1
                print(f'  ✅ Successfully processed {os.path.basename(md_file)}')
            else:
                failed_count += 1
                print(f'  ❌ Error processing {os.path.basename(md_file)}, exit code: {result.returncode}')
                if result.stderr:
                    print(f'     Error: {result.stderr[:200]}...')
                    
        except subprocess.TimeoutExpired:
            failed_count += 1
            print(f'  ⏰ Timeout processing {os.path.basename(md_file)} (>5 minutes)')
        except Exception as e:
            failed_count += 1
            print(f'  ❌ Exception processing {os.path.basename(md_file)}: {e}')
    
    print(f"\nBatch {batch_num} Summary:")
    print(f"  ✅ Successfully processed: {processed_count}")
    print(f"  ❌ Failed: {failed_count}")
    print(f"  📁 Results saved to: {output_dir}")
    
    return failed_count == 0

def main():
    """Main processing function"""
    print("Starting batch processing of markdown files...")
    
    # Find all batch directories
    batch_dirs = []
    for i in range(1, 6):
        batch_dir = f'markdown_batch_{i}'
        if os.path.exists(batch_dir):
            batch_dirs.append((batch_dir, i))
    
    if not batch_dirs:
        print("No batch directories found!")
        return
    
    print(f"Found {len(batch_dirs)} batch directories to process")
    
    # Process each batch
    total_processed = 0
    total_failed = 0
    successful_batches = 0
    
    for batch_dir, batch_num in batch_dirs:
        success = process_batch(batch_dir, batch_num)
        if success:
            successful_batches += 1
        
        # Count files in output directory
        output_dir = f'batch_{batch_num}_results'
        if os.path.exists(output_dir):
            result_files = len(glob.glob(os.path.join(output_dir, '*')))
            total_processed += result_files
    
    print(f"\n{'='*60}")
    print("FINAL SUMMARY")
    print(f"{'='*60}")
    print(f"Batches processed: {len(batch_dirs)}")
    print(f"Successful batches: {successful_batches}")
    print(f"Total files processed: {total_processed}")
    print(f"Results directories created:")
    for i in range(1, 6):
        output_dir = f'batch_{i}_results'
        if os.path.exists(output_dir):
            file_count = len(glob.glob(os.path.join(output_dir, '*')))
            print(f"  - {output_dir}: {file_count} files")

def process_single_batch(batch_number):
    """Process a single specific batch"""
    batch_dir = f'markdown_batch_{batch_number}'
    if not os.path.exists(batch_dir):
        print(f"Batch directory {batch_dir} not found!")
        return
    
    process_batch(batch_dir, batch_number)

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # Process specific batch if batch number is provided
        try:
            batch_num = int(sys.argv[1])
            if 1 <= batch_num <= 5:
                print(f"Processing only batch {batch_num}...")
                process_single_batch(batch_num)
            else:
                print("Batch number must be between 1 and 5")
        except ValueError:
            print("Invalid batch number. Usage: python run_batch_processing.py [batch_number]")
    else:
        # Process all batches
        main()
