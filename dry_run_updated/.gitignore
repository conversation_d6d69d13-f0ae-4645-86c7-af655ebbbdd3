*.ipynb
*.pyc
*.db
*.db-shm
*.db-wal
*.db-journal
*.db-trace
*.db-lock
tmp_*

# Directories
tmp/
data/
db/
logs/
__pycache__/
*.egg-info/
build/
dist/

# Python bytecode
*.pyo
*.pyd
*.pyw
*.pyz
*.pywz
*.pyzw
*.cpython-*

# Distribution / packaging
*.egg
*.whl

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Cython debug symbols
cython_debug/

# JSON files
other_params.json
*.jsonl
*.jsonl.gz
*.jsonl.gz.part
*.jsonl.gz.part.tmp
.vscode

experiments/
output/
.logfire/