import sys
from typing import List, Optional, Literal, Dict, Annotated, Union, TypeVar, Generic, Any, Type, Callable
from pydantic import BaseModel, Field, model_validator
from enum import Enum
import operator

# All the enum classes with NONE option
class AntigenExpressionLevels(str, Enum):
    HIGH = "High Antigen Expression"
    LOW = "Low Antigen Expression"
    MODERATE = "Moderate Antigen Expression"
    NONE = "Not Specified"

class ConcentrationComponents(str, Enum):
    INTACT_ADC = "Intact ADC"
    FREE_PAYLOAD = "Free Payload"
    FREE_ANTIBODY = "Free Antibody"
    OTHER = "Other Components"

class ExperimentType(str, Enum):
    IN_VIVO = "In Vivo Studies"
    IN_VITRO = "In Vitro Studies"
    EX_VIVO = "Ex Vivo Studies"
    NONE = "NONE"
    

class ModelType(str, Enum):
    CELL_LINE = "Cell Line Model"
    CDX = "Cell Line-Derived Xenograft (CDX)"
    PDX = "Patient-Derived Xenograft (PDX)"
    ORGANOID = "Organoid Model"
    SYNGENEIC = "Syngeneic Model"
    TISSUE_SPECIMENS = "Tissue Specimens"
    TRANSGENIC = "Transgenic Model"
    NON_CELL_BASED = "Non-cell based Model"
    RODENT_MODELS = "Rodent Models"
    NON_HUMAN_PRIMATES = "Non-Human Primate Models"
    NONE = "NONE"
   

class LinkerType(str, Enum):
    CLEAVABLE = "Cleavable Linker"
    NON_CLEAVABLE = "Non-cleavable Linker"
    NONE = "NONE"
    

class AntibodyClonality(str, Enum):
    MONOCLONAL = "Monoclonal Antibody (mAb)"
    POLYCLONAL = "Polyclonal Antibody (pAb)"
    NONE = "NONE"     
    

class AntibodySpecies(str, Enum):
    MURINE = "Murine"
    CHIMERIC = "Chimeric"
    HUMANIZED = "Humanized"
    NONE = "NONE"     
    
class AntibodyIsotype(str, Enum):
    IGG = "IgG"
    IGM = "IgM"
    IGA = "IgA"
    IGE = "IgE"
    IGD = "IgD"
    NONE = "NONE"     
   

class EndpointType(str, Enum):
    SAFETY = "safety"
    EFFICACY = "efficacy"
    PHARMACOKINETICS = "pharmacokinetics"
    PHARMACODYNAMICS = "pharmacodynamics"
    BIOMARKER = "biomarker"
    NONE = "NONE"     

class AntibodyDrugConjugateType(str, Enum):
    INVESTIGATIVE = "Investigative"
    REFERENCE = "Reference"

class EndpointName(BaseModel):
    # Biomarker endpoints
    ANTIGEN_EXPRESSION: bool = Field(..., description="Boolean value indicating antigen expression levels in biological samples categorized qualitatively into High or Low based on predefined thresholds")
    SPECIFIC_ANTIGEN_EXPRESSION_H_SCORE: bool = Field(..., description="Boolean value indicating H-score measurement (0-300) combining staining intensity and percentage of positive cells")
    
    # Efficacy endpoints
    ADC_EC50: bool = Field(..., description="Boolean value indicating concentration of intact ADC required for 50% maximum biological effect")
    PAYLOAD_EC50: bool = Field(..., description="Boolean value indicating concentration of free cytotoxic drug required for 50% maximum biological effect")
    ADC_KD: bool = Field(..., description="Boolean value indicating dissociation constant quantifying binding affinity between ADC and target antigen")
    ADC_INTERNALIZATION: bool = Field(..., description="Boolean value indicating proportion of ADC successfully internalized by target cells within specified timeframe")
    ADC_TREATMENT_CONCENTRATION: bool = Field(..., description="Boolean value indicating defined amount of ADC applied to co-culture system to evaluate bystander killing effect")
    BM_POS_CELL_DECREASE: bool = Field(..., description="Boolean value indicating reduction in biomarker-positive cells following ADC treatment")
    BM_NEG_CELL_DECREASE: bool = Field(..., description="Boolean value indicating reduction in biomarker-negative cells due to bystander killing effect")
    ADC_IC50: bool = Field(..., description="Boolean value indicating concentration of intact ADC required to inhibit 50% of cell proliferation or viability")
    PAYLOAD_IC50: bool = Field(..., description="Boolean value indicating concentration of free cytotoxic drug required to inhibit 50% of cell proliferation or viability")
    ADC_GI50: bool = Field(..., description="Boolean value indicating concentration required to inhibit 50% of net cell growth compared to controls")
    ANTI_TUMOR_ACTIVITY_DOSE: bool = Field(..., description="Boolean value indicating dose of ADC administered per kg of body weight in animal studies")
    TUMOR_GROWTH_INHIBITION: bool = Field(..., description="Boolean value indicating percentage reduction in tumor size or growth rate compared to control group")
    OBJECTIVE_RESPONSE_RATE: bool = Field(..., description="Boolean value indicating proportion of tumor-bearing animals with measurable tumor volume reduction")
    
    # PK/PD endpoints
    DAR_TIME_TO_50_PERCENT: bool = Field(..., description="Boolean value indicating time for Drug-to-Antibody Ratio to decrease by 50% in circulation")
    PAYLOAD_RELEASE: bool = Field(..., description="Boolean value indicating percentage of ADC payload released into circulation or tumor site")
    PK_DOSE: bool = Field(..., description="Boolean value indicating administered dose of ADC in preclinical models for ADME evaluation")
    ADC_CMAX: bool = Field(..., description="Boolean value indicating peak plasma concentration of full ADC after administration")
    ADC_AUC: bool = Field(..., description="Boolean value indicating total systemic exposure of ADC over time")
    ADC_T_HALF: bool = Field(..., description="Boolean value indicating time for plasma concentration of full ADC to decrease by 50%")
    TAB_CMAX: bool = Field(..., description="Boolean value indicating peak plasma concentration of total antibody")
    TAB_AUC: bool = Field(..., description="Boolean value indicating total exposure of total antibody")
    TAB_T_HALF: bool = Field(..., description="Boolean value indicating time for total antibody to decrease by 50%")
    PAYLOAD_CMAX: bool = Field(..., description="Boolean value indicating peak plasma concentration of released cytotoxic drug")
    PAYLOAD_AUC: bool = Field(..., description="Boolean value indicating total systemic exposure of released cytotoxic payload")
    PAYLOAD_T_HALF: bool = Field(..., description="Boolean value indicating time for plasma concentration of released cytotoxic payload to decrease by 50%")
    ALBUMIN_RECONJUGATION: bool = Field(..., description="Boolean value indicating binding of drug/payload from ADC to serum albumin")
    ANTI_DRUG_ANTIBODIES: bool = Field(..., description="Boolean value indicating antibodies produced by immune system in response to ADC")
    
    # Toxicity endpoints
    TOXICOLOGY_DOSE: bool = Field(..., description="Boolean value indicating drug dose administered per kg in animal toxicity studies")
    TOXICITY_FREQUENCY: bool = Field(..., description="Boolean value indicating dosing frequency of ADC to minimize toxicity")
    LETHAL_DOSE: bool = Field(..., description="Boolean value indicating dose causing death in preclinical animal models")
    HNSTD: bool = Field(..., description="Boolean value indicating maximum dose without severe/life-threatening toxic effects")
    DECREASED_FOOD_CONSUMPTION: bool = Field(..., description="Boolean value indicating reduced food intake due to ADC")
    GI_ISSUES: bool = Field(..., description="Boolean value indicating gastrointestinal toxicity observed")
    DECREASED_BODY_WEIGHT: bool = Field(..., description="Boolean value indicating weight loss due to ADC administration")
    REDUCED_RED_BLOOD_COUNT: bool = Field(..., description="Boolean value indicating decrease in circulating red blood cells")
    REDUCED_HEMOGLOBIN: bool = Field(..., description="Boolean value indicating lower hemoglobin levels")
    RETICULOCYTE: bool = Field(..., description="Boolean value indicating percentage or count of immature red blood cells")
    REDUCED_ALBUMIN: bool = Field(..., description="Boolean value indicating decrease in serum albumin levels")
    WHITE_BLOOD_CELLS: bool = Field(..., description="Boolean value indicating total count of circulating white blood cells")
    REDUCED_LYMPHOCYTES: bool = Field(..., description="Boolean value indicating decrease in lymphocyte count")
    REDUCED_NEUTROPHILS: bool = Field(..., description="Boolean value indicating decrease in neutrophil count")
    INCREASED_AST: bool = Field(..., description="Boolean value indicating elevated AST liver enzyme levels")
    INCREASED_ALT: bool = Field(..., description="Boolean value indicating elevated ALT liver enzyme levels")
    LUNG_INFLAMMATION: bool = Field(..., description="Boolean value indicating inflammatory response in lung tissue")
    INTERSTITIAL_LUNG_DISEASE: bool = Field(..., description="Boolean value indicating lung inflammation and fibrosis")
    TARGET_ORGAN_ISSUES: bool = Field(..., description="Boolean value indicating organ-specific toxicities caused by ADCs")
    
    # New endpoints
    HETEROGENEITY_INDEX: bool = Field(..., description="Boolean value indicating measure of variability in drug-to-antibody ratio (DAR) among molecules in an ADC product")
    ANTIGEN_SHEDDING: bool = Field(..., description="Boolean value indicating the process by which target antigen is cleaved and released from cancer cell surface into bloodstream")

class Endpoint(BaseModel):
    """Base class for all endpoints"""
    measured_value: str = Field(None, description="The value of the endpoint")
    measured_time: str = Field(None, description="The timepoint at which the endpoint is measured")
    measured_concentration: str = Field(None, description="The concentration of the ADC at the timepoint of measurement")

# Refactored Pydantic Models

# ADC
class AntibodyDrugConjugate(BaseModel):
    """Information about ADC: Antibody Drug Conjugate with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    adc_name: str = Field(..., description="The name of the Antibody Drug Conjugate")
    adc_company: str = Field(..., description="The company that developed the above ADC")

    adc_type: AntibodyDrugConjugateType = Field(..., description="The type of the above ADC, whether it is an investigative or reference ADC. Investigative ADC is the one that is being tested in the preclinical experiment, while reference ADC is the one that is already approved and used as a benchmark for comparison or which is just mentioned in the study. Most importantly, reference ADC is not experimented on in the preclinical study.")
    #Antibody fields
    antibody_name: str = Field(..., description="The name of the antibody used in the above ADC")
    antibody_binding_epitope: str = Field(..., description="The specific region on an antigen that is recognized and bound by an antibody. It is the molecular address where the antibody docks to initiate an immune response or therapeutic effect.It is usually reprsented as short peptise sequence of amino acids")
    antibody_binding_epitope_location: str = Field(..., description="The location of the above antibody binding epitope on the antigen. It can be membrane-proximal or membrane-distal")
    adc_species_cross_reactivity: str = Field(..., description="the ability of the ADC to bind to the same or similar antigen in different animal species (e.g., mouse, rat, monkey, dog) as it does in humans.")
    antibody_clonality: AntibodyClonality = Field(..., description="The type of clonality for the above antibody")
    antibody_species: AntibodySpecies = Field(..., description="The species origin of the above antibody")
    antibody_isotype: AntibodyIsotype = Field(..., description="The isotype classification of the above antibody")
    #Payload fields
    payload_name: str = Field(..., description="The name of the chemical compound used as payload to be delivered using above ADC")    
    payload_target: str = Field(..., description="The specific molecular target or pathway that the above payload is designed to affect")
    #Linker fields
    linker_name: str = Field(..., description="The name of the chemical compound used as a linker to bind antibody and payload together in the above ADC")
    linker_type: LinkerType = Field(..., description="The cleavable property of the above linker")
    #Antigen fields
    antigen_name: str = Field(..., description="The name of the antigen targeted by the above ADC")
    
    # Additional fields
    ss_conjugation: bool = Field(None, description="Indicates whether the ADC is conjugated at a specific site on the antibody")
    ss_conjugation_technology: str = Field(None, description="Technology used for site-specific conjugation (e.g., engineered amino acids, enzymatic methods)")
    conjugation_amino_acid: str = Field(None, description="Amino acid on the antibody used for drug or payload attachment (Cysteine, Lysine, Glutamine, Glycan, etc.)")
    conjugation_sites: List[str] = Field(default_factory=list, description="Specific locations on the antibody where the drug is conjugated")
    drug_to_antibody_ratio: str = Field(None, description="Average number of payload molecules conjugated per antibody")
    clinical_trial_phase: str = Field(None, description="Current clinical trial phase of the ADC")
    clinical_data_availability: bool = Field(None, description="Indicates whether clinical trial data is available")

# TODO - Edit adc agent prompt - to call load_adc_names_into_memory Tool to extract and save the ADC names in memory

class ExperimentalModel(BaseModel):
    """Information about experimental model used in the preclinical experiment for given ADC with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    model_name: str = Field(..., description="The name of the model used in the experiment for given ADC. This is cell line name or tissue specimen name or the organism name Only! DO NOT add the model type (e.g. CDX, PDX, cell line, Xenograft etc) in this field since the same model culd be used as cell line, xenograft, tissue etc!! Just keep the name as an identifier here!")
    clinical_human_related: bool = Field(..., description="Indicates whether clinical data is available for this model.Clinical data refers to any information collected about a human subject in the context of healthcare delivery, clinical trials, or medical research. It encompasses a wide range of data types that describe a patient's health status, medical history, diagnostic results, treatments, and outcomes." 
    )
    cancer_type: str = Field(..., description="A cancer type refers to the broad, primary classification of cancer, based on the organ or tissue where the cancer originates. It is essentially the location in the body where the cancerous cells began to form.")
    cancer_subtype: Optional[str] = Field(None, description="A cancer subtype is a further classification within a cancer type, based on specific histological, genetic, or molecular characteristics of the tumor cells. Subtypes can influence the cancer's behavior, its response to treatment, and its prognosis.")



class Experiment(BaseModel):
    """Information about the experiment conducted on the experimental model for given ADC """
    experiment_type: ExperimentType = Field(..., description="The type of experiment conducted on this model for given ADC")
    model_type: ModelType = Field(..., description="The type of model used in the experiment for given ADC")

    @model_validator(mode='after')
    def validate_experiment_model_consistency(self):
        """Validate logical consistency between experiment_type and model_type."""
        experiment_type = self.experiment_type
        model_type = self.model_type

        # Define valid combinations using enum values
        valid_combinations = {
            ExperimentType.IN_VITRO: {ModelType.CELL_LINE, ModelType.NON_CELL_BASED, ModelType.ORGANOID, ModelType.NONE},
            ExperimentType.EX_VIVO: {ModelType.TISSUE_SPECIMENS, ModelType.NONE},
            ExperimentType.IN_VIVO: {
                ModelType.CDX, ModelType.PDX, ModelType.SYNGENEIC,
                ModelType.TRANSGENIC, ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE
            },
            ExperimentType.NONE: {
                ModelType.CELL_LINE, ModelType.NON_CELL_BASED, ModelType.ORGANOID, ModelType.TISSUE_SPECIMENS,
                ModelType.CDX, ModelType.PDX, ModelType.SYNGENEIC, ModelType.TRANSGENIC,
                ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE
            }
        }

        if model_type not in valid_combinations[experiment_type]:
            valid_options = [option.value for option in valid_combinations[experiment_type]]
            raise ValueError(
                f"Invalid combination: {experiment_type.value} experiments cannot use '{model_type.value}' models. Recheck your answer!"
            )

        return self

# Specialized endpoint models named exactly after the EndpointName enum values
class AntigenExpression(Experiment):
    """Endpoint Information about Antigen Expression for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: AntigenExpressionLevels = Field(None, description="Categorization of the level of antigen expression in biological samples such as tumor cell lines, primary cells, or tissue specimens, categorized as high, low, or moderate based on predefined thresholds")

    @model_validator(mode='after')
    def validate_model_type_for_antigen_expression(self):
        """Validate that model_type is compatible with AntigenExpression endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.ORGANOID, ModelType.TISSUE_SPECIMENS, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for AntigenExpression: {self.model_type.value}. Antigen Expression cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class SpecificAntigenExpressionHScore(Experiment):
    """Endpoint measurement of antigen expression using H-score for ADC target validation with supporting citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="H-score value (0-300) representing semi-quantitative immunohistochemistry assessment of antigen expression intensity and distribution in tissue specimens or immunocytochemistry in cell lines")

    @model_validator(mode='after')
    def validate_model_type_for_specific_antigen_expression_h_score(self):
        """Validate that model_type is compatible with SpecificAntigenExpressionHScore endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.ORGANOID, ModelType.TISSUE_SPECIMENS, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for SpecificAntigenExpressionHScore: {self.model_type.value}. H Score cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class AdcEc50(Experiment):
    """Endpoint Information about a unique measurement of ADC EC50 for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The concentration of the intact ADC molecule required to achieve 50% of its maximum biological effect, typically expressed in nM, pM, µM, ng/mL, or µg/mL")
    measured_time: str = Field(None, description="The specific time period after ADC administration that the EC50 value is measured")

    @model_validator(mode='after')
    def validate_model_type_for_adc_ec50(self):
        """Validate that model_type is compatible with AdcEc50 endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.NON_CELL_BASED, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for AdcEc50: {self.model_type.value}. ADC EC50 cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class PayloadEc50(Experiment):
    """Endpoint Information about a unique measurement of Payload EC50 for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The concentration of the free (unconjugated) cytotoxic drug required to achieve 50% of its maximum biological effect, typically expressed in nM, pM, µM, ng/mL, or µg/mL")
    measured_time: str = Field(None, description="The specific time period after payload administration that the EC50 value is measured")

    @model_validator(mode='after')
    def validate_model_type_for_payload_ec50(self):
        """Validate that model_type is compatible with PayloadEc50 endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.NON_CELL_BASED, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for PayloadEc50: {self.model_type.value}. Payload EC50 cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class AdcKd(Experiment):
    """Endpoint Information about ADC Kd (dissociation constant) for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The dissociation constant (Kd) quantifying the binding affinity between the antibody component of an ADC and its target antigen, typically expressed in M, mM, µM, nM, or pM")
    
    @model_validator(mode='after')
    def validate_model_type_for_adc_kd(self):
        """Validate that model_type is compatible with AdcKd endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.NON_CELL_BASED, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for AdcKd: {self.model_type.value}. ADC Kd cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class AdcInternalization(Experiment):
    """Endpoint Information about ADC internalization for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The proportion of the total administered ADC that is successfully internalized by target cells, typically expressed as a percentage")
    measured_time: str = Field(None, description="The specific time frame within which the ADC internalization is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_adc_internalization(self):
        """Validate that model_type is compatible with AdcInternalization endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for AdcInternalization: {self.model_type.value}. ADC Internalization cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class AdcTreatmentConcentration(Experiment):
    """Endpoint Information about ADC treatment concentration for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The defined amount of ADC applied to a co-culture system of biomarker-positive and biomarker-negative cells, typically expressed in molar units such as nM or pM")
    
    @model_validator(mode='after')
    def validate_model_type_for_adc_treatment_concentration(self):
        """Validate that model_type is compatible with AdcTreatmentConcentration endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for AdcTreatmentConcentration: {self.model_type.value}. ADC Treatment Concentration cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class BmPosCellDecrease(Experiment):
    """Endpoint Information about biomarker-positive cell decrease for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The reduction in the number or viability of cells that express the target antigen following treatment with an ADC, typically expressed as percentage decrease relative to untreated control")
    measured_time: str = Field(None, description="The time point at which the cell decrease is measured after ADC treatment")
    
    @model_validator(mode='after')
    def validate_model_type_for_bm_pos_cell_decrease(self):
        """Validate that model_type is compatible with BmPosCellDecrease endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for BmPosCellDecrease: {self.model_type.value}. Biomarker-positive cell decrease cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class BmNegCellDecrease(Experiment):
    """Endpoint Information about biomarker-negative cell decrease for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The reduction in the number or viability of cells that do not express the target antigen following treatment with an ADC, typically expressed as percentage decrease relative to untreated control")
    measured_time: str = Field(None, description="The time point at which the cell decrease is measured after ADC treatment")
    
    @model_validator(mode='after')
    def validate_model_type_for_bm_neg_cell_decrease(self):
        """Validate that model_type is compatible with BmNegCellDecrease endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for BmNegCellDecrease: {self.model_type.value}. Biomarker-negative cell decrease cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class AdcIc50(Experiment):
    """Endpoint Information about ADC IC50 for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The concentration of the intact antibody-drug conjugate required to inhibit 50% of cell proliferation or viability, typically expressed in nM, pM, µM, ng/mL, or µg/mL")
    measured_time: str = Field(None, description="The time point at which the IC50 is measured after ADC treatment")
    
    @model_validator(mode='after')
    def validate_model_type_for_adc_ic50(self):
        """Validate that model_type is compatible with AdcIc50 endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.NON_CELL_BASED, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for AdcIc50: {self.model_type.value}. ADC IC50 cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class PayloadIc50(Experiment):
    """Endpoint Information about Payload IC50 for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The concentration of the free (unconjugated) cytotoxic drug required to inhibit 50% of cell proliferation or viability, typically expressed in nM, pM, µM, ng/mL, or µg/mL")
    measured_time: str = Field(None, description="The time point at which the IC50 is measured after payload treatment")
    
    @model_validator(mode='after')
    def validate_model_type_for_payload_ic50(self):
        """Validate that model_type is compatible with PayloadIc50 endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.NON_CELL_BASED, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for PayloadIc50: {self.model_type.value}. Payload IC50 cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class AdcGi50(Experiment):
    """Endpoint Information about ADC GI50 for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The concentration of an ADC required to inhibit 50% of net cell growth compared to untreated controls, typically expressed in nM, pM, µM, ng/mL, or µg/mL")
    measured_time: str = Field(None, description="The time period over which the growth inhibition is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_adc_gi50(self):
        """Validate that model_type is compatible with AdcGi50 endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for AdcGi50: {self.model_type.value}. ADC GI50 cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class AntiTumorActivityDose(Experiment):
    """Endpoint Information about anti-tumor activity dose for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="Dose of ADC administered per kg of body weight in animal studies, typically expressed as mg/kg or mpk")
    measured_frequency: str = Field(None, description="The frequency of dose administration")
    
    @model_validator(mode='after')
    def validate_model_type_for_anti_tumor_activity_dose(self):
        """Validate that model_type is compatible with AntiTumorActivityDose endpoint."""
        allowed_model_types = {ModelType.CDX, ModelType.PDX, ModelType.SYNGENEIC, ModelType.TRANSGENIC, ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for AntiTumorActivityDose: {self.model_type.value}. Anti-tumor activity dose cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class TumorGrowthInhibition(Experiment):
    """Endpoint Information about tumor growth inhibition for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The percentage reduction in tumor size or growth rate compared to a control group")
    measured_time: str = Field(None, description="The time point at which tumor growth inhibition is measured after treatment")
    measured_dose: str = Field(None, description="The dose of ADC at which the tumor growth inhibition is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_tumor_growth_inhibition(self):
        """Validate that model_type is compatible with TumorGrowthInhibition endpoint."""
        allowed_model_types = {ModelType.CDX, ModelType.PDX, ModelType.SYNGENEIC, ModelType.TRANSGENIC, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for TumorGrowthInhibition: {self.model_type.value}. Tumor growth inhibition cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class ObjectiveResponseRate(Experiment):
    """Endpoint Information about objective response rate for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The proportion of tumor-bearing animals that exhibit a measurable reduction in tumor volume following treatment, expressed as a percentage")
    measured_dose: str = Field(None, description="The dose of ADC at which the objective response rate is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_objective_response_rate(self):
        """Validate that model_type is compatible with ObjectiveResponseRate endpoint."""
        allowed_model_types = {ModelType.CDX, ModelType.PDX, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for ObjectiveResponseRate: {self.model_type.value}. Objective response rate cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class DarTimeTo50Percent(Experiment):
    """Endpoint Information about DAR time to 50% for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The time required for the Drug-to-Antibody Ratio (DAR) to decrease by 50% in circulation, typically expressed in hours or days")
    
    @model_validator(mode='after')
    def validate_model_type_for_dar_time_to_50_percent(self):
        """Validate that model_type is compatible with DarTimeTo50Percent endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for DarTimeTo50Percent: {self.model_type.value}. DAR time to 50% cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class PayloadRelease(Experiment):
    """Endpoint Information about payload release for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The percentage of ADC payload released into circulation or tumor site")
    measured_time: str = Field(None, description="The time point at which payload release is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_payload_release(self):
        """Validate that model_type is compatible with PayloadRelease endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.CDX, ModelType.PDX, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for PayloadRelease: {self.model_type.value}. Payload release cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class PkDose(Experiment):
    """Endpoint Information about PK dose for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The administered dose of an ADC in preclinical models to evaluate its absorption, distribution, metabolism, and excretion (ADME)")
    
    @model_validator(mode='after')
    def validate_model_type_for_pk_dose(self):
        """Validate that model_type is compatible with PkDose endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for PkDose: {self.model_type.value}. PK dose cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class AdcCmax(Experiment):
    """Endpoint Information about ADC Cmax for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The peak plasma concentration of the full ADC after administration, typically expressed as µg/mL or ng/mL")
    measured_dose: str = Field(None, description="The dose at which the Cmax is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_adc_cmax(self):
        """Validate that model_type is compatible with AdcCmax endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for AdcCmax: {self.model_type.value}. ADC Cmax cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class AdcAuc(Experiment):
    """Endpoint Information about ADC AUC for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The total systemic exposure of the ADC over time, typically expressed as µg*h/mL, mg*h/L, or ng*h/mL")
    measured_dose: str = Field(None, description="The dose at which the AUC is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_adc_auc(self):
        """Validate that model_type is compatible with AdcAuc endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for AdcAuc: {self.model_type.value}. ADC AUC cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class HeterogeneityIndex(Experiment):
    """Endpoint Information about Heterogeneity Index for the given ADC with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="A numerical value representing the variability in drug-to-antibody ratio (DAR) among molecules in an ADC product")
    
    @model_validator(mode='after')
    def validate_model_type_for_heterogeneity_index(self):
        """Validate that model_type is compatible with HeterogeneityIndex endpoint."""
        allowed_model_types = {ModelType.NON_CELL_BASED, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for HeterogeneityIndex: {self.model_type.value}. Heterogeneity Index cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class AntigenShedding(Experiment):
    """Endpoint Information about Antigen Shedding for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The rate or amount of target antigen cleaved and released from cancer cell surface into bloodstream, typically expressed as a concentration or percentage")
    measured_time: str = Field(None, description="The time point at which antigen shedding is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_antigen_shedding(self):
        """Validate that model_type is compatible with AntigenShedding endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.CDX, ModelType.PDX, ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for AntigenShedding: {self.model_type.value}. Antigen Shedding cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class AdcTHalf(Experiment):
    """Endpoint Information about ADC half-life for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The time required for the plasma concentration of the full ADC to decrease by 50%, typically expressed in hours or days")
    measured_dose: str = Field(None, description="The dose at which the half-life is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_adc_t_half(self):
        """Validate that model_type is compatible with AdcTHalf endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for AdcTHalf: {self.model_type.value}. ADC half-life cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class TabCmax(Experiment):
    """Endpoint Information about total antibody Cmax for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The peak plasma concentration of total antibody, typically expressed as µg/mL")
    measured_dose: str = Field(None, description="The dose at which the Cmax is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_tab_cmax(self):
        """Validate that model_type is compatible with TabCmax endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for TabCmax: {self.model_type.value}. Total antibody Cmax cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class TabAuc(Experiment):
    """Endpoint Information about total antibody AUC for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The total exposure of total antibody, typically expressed as µg*h/mL, mg*h/L, or ng*h/mL")
    measured_dose: str = Field(None, description="The dose at which the AUC is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_tab_auc(self):
        """Validate that model_type is compatible with TabAuc endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for TabAuc: {self.model_type.value}. Total antibody AUC cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class TabTHalf(Experiment):
    """Endpoint Information about total antibody half-life for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The time required for the total antibody to decrease by 50%, typically expressed in hours or days")
    measured_dose: str = Field(None, description="The dose at which the half-life is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_tab_t_half(self):
        """Validate that model_type is compatible with TabTHalf endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for TabTHalf: {self.model_type.value}. Total antibody half-life cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class PayloadCmax(Experiment):
    """Endpoint Information about payload Cmax for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The peak plasma concentration of the released cytotoxic drug, typically expressed as µg/mL or ng/mL")
    measured_dose: str = Field(None, description="The dose at which the Cmax is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_payload_cmax(self):
        """Validate that model_type is compatible with PayloadCmax endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for PayloadCmax: {self.model_type.value}. Payload Cmax cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class PayloadAuc(Experiment):
    """Endpoint Information about payload AUC for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The total systemic exposure of released cytotoxic payload, typically expressed as ng*h/mL")
    measured_dose: str = Field(None, description="The dose at which the AUC is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_payload_auc(self):
        """Validate that model_type is compatible with PayloadAuc endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for PayloadAuc: {self.model_type.value}. Payload AUC cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class PayloadTHalf(Experiment):
    """Endpoint Information about payload half-life for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The time required for the plasma concentration of released cytotoxic payload to decrease by 50%, typically expressed in hours or days")
    measured_dose: str = Field(None, description="The dose at which the half-life is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_payload_t_half(self):
        """Validate that model_type is compatible with PayloadTHalf endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for PayloadTHalf: {self.model_type.value}. Payload half-life cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class AlbuminReconjugation(Experiment):
    """Endpoint Information about albumin reconjugation for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The extent of binding of drug/payload from ADC to serum albumin, typically expressed as a percentage or concentration")
    measured_time: str = Field(None, description="The time point at which albumin reconjugation is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_albumin_reconjugation(self):
        """Validate that model_type is compatible with AlbuminReconjugation endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for AlbuminReconjugation: {self.model_type.value}. Albumin reconjugation cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class AntiDrugAntibodies(Experiment):
    """Endpoint Information about anti-drug antibodies for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The presence or level of antibodies produced by the immune system in response to ADC")
    measured_time: str = Field(None, description="The time point at which anti-drug antibodies are measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_anti_drug_antibodies(self):
        """Validate that model_type is compatible with AntiDrugAntibodies endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for AntiDrugAntibodies: {self.model_type.value}. Anti-drug antibodies cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class ToxicologyDose(Experiment):
    """Endpoint Information about toxicology dose for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The drug dose administered per kg in animal toxicity studies")
    
    @model_validator(mode='after')
    def validate_model_type_for_toxicology_dose(self):
        """Validate that model_type is compatible with ToxicologyDose endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for ToxicologyDose: {self.model_type.value}. Toxicology dose cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class ToxicityFrequency(Experiment):
    """Endpoint Information about toxicity frequency for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The dosing frequency of ADC to minimize toxicity")
    
    @model_validator(mode='after')
    def validate_model_type_for_toxicity_frequency(self):
        """Validate that model_type is compatible with ToxicityFrequency endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for ToxicityFrequency: {self.model_type.value}. Toxicity frequency cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class LethalDose(Experiment):
    """Endpoint Information about lethal dose for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The dose causing death in preclinical animal models, typically expressed in mg/kg")
    
    @model_validator(mode='after')
    def validate_model_type_for_lethal_dose(self):
        """Validate that model_type is compatible with LethalDose endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for LethalDose: {self.model_type.value}. Lethal dose cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class Hnstd(Experiment):
    """Endpoint Information about highest non-severely toxic dose for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The maximum dose without severe/life-threatening toxic effects")
    
    @model_validator(mode='after')
    def validate_model_type_for_hnstd(self):
        """Validate that model_type is compatible with Hnstd endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for Hnstd: {self.model_type.value}. HNSTD cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class DecreasedFoodConsumption(Experiment):
    """Endpoint Information about decreased food consumption for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The reduction in food intake due to ADC, typically expressed as a percentage or amount")
    measured_dose: str = Field(None, description="The dose at which decreased food consumption is observed")
    
    @model_validator(mode='after')
    def validate_model_type_for_decreased_food_consumption(self):
        """Validate that model_type is compatible with DecreasedFoodConsumption endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for DecreasedFoodConsumption: {self.model_type.value}. Decreased food consumption cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class GiIssues(Experiment):
    """Endpoint Information about GI issues for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The gastrointestinal toxicity observed, typically described qualitatively or quantitatively")
    measured_dose: str = Field(None, description="The dose at which GI issues are observed")
    
    @model_validator(mode='after')
    def validate_model_type_for_gi_issues(self):
        """Validate that model_type is compatible with GiIssues endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for GiIssues: {self.model_type.value}. GI issues cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class DecreasedBodyWeight(Experiment):
    """Endpoint Information about decreased body weight for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The weight loss due to ADC administration, typically expressed as a percentage or amount")
    measured_dose: str = Field(None, description="The dose at which decreased body weight is observed")
    
    @model_validator(mode='after')
    def validate_model_type_for_decreased_body_weight(self):
        """Validate that model_type is compatible with DecreasedBodyWeight endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for DecreasedBodyWeight: {self.model_type.value}. Decreased body weight cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class ReducedRedBloodCount(Experiment):
    """Endpoint Information about reduced red blood count for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The decrease in circulating red blood cells, typically expressed as a count or percentage")
    measured_dose: str = Field(None, description="The dose at which reduced red blood count is observed")
    
    @model_validator(mode='after')
    def validate_model_type_for_reduced_red_blood_count(self):
        """Validate that model_type is compatible with ReducedRedBloodCount endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for ReducedRedBloodCount: {self.model_type.value}. Reduced red blood count cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class ReducedHemoglobin(Experiment):
    """Endpoint Information about reduced hemoglobin for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The lower hemoglobin levels, typically expressed in g/dL")
    measured_dose: str = Field(None, description="The dose at which reduced hemoglobin is observed")
    
    @model_validator(mode='after')
    def validate_model_type_for_reduced_hemoglobin(self):
        """Validate that model_type is compatible with ReducedHemoglobin endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for ReducedHemoglobin: {self.model_type.value}. Reduced hemoglobin cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class Reticulocyte(Experiment):
    """Endpoint Information about reticulocyte for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The percentage or absolute count of immature red blood cells in circulation")
    measured_dose: str = Field(None, description="The dose at which reticulocyte count is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_reticulocyte(self):
        """Validate that model_type is compatible with Reticulocyte endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for Reticulocyte: {self.model_type.value}. Reticulocyte count cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class ReducedAlbumin(Experiment):
    """Endpoint Information about reduced albumin for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The decrease in serum albumin levels, typically expressed in g/dL")
    measured_dose: str = Field(None, description="The dose at which reduced albumin is observed")
    
    @model_validator(mode='after')
    def validate_model_type_for_reduced_albumin(self):
        """Validate that model_type is compatible with ReducedAlbumin endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for ReducedAlbumin: {self.model_type.value}. Reduced albumin cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class WhiteBloodCells(Experiment):
    """Endpoint Information about white blood cells for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The total count of circulating white blood cells")
    measured_dose: str = Field(None, description="The dose at which white blood cell count is measured")
    
    @model_validator(mode='after')
    def validate_model_type_for_white_blood_cells(self):
        """Validate that model_type is compatible with WhiteBloodCells endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for WhiteBloodCells: {self.model_type.value}. White blood cell count cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class ReducedLymphocytes(Experiment):
    """Endpoint Information about reduced lymphocytes for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The decrease in lymphocyte count, typically expressed as a count or percentage")
    measured_dose: str = Field(None, description="The dose at which reduced lymphocyte count is observed")
    
    @model_validator(mode='after')
    def validate_model_type_for_reduced_lymphocytes(self):
        """Validate that model_type is compatible with ReducedLymphocytes endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for ReducedLymphocytes: {self.model_type.value}. Reduced lymphocyte count cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class ReducedNeutrophils(Experiment):
    """Endpoint Information about reduced neutrophils for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The decrease in neutrophil count, typically expressed as a count or percentage")
    measured_dose: str = Field(None, description="The dose at which reduced neutrophil count is observed")
    
    @model_validator(mode='after')
    def validate_model_type_for_reduced_neutrophils(self):
        """Validate that model_type is compatible with ReducedNeutrophils endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for ReducedNeutrophils: {self.model_type.value}. Reduced neutrophil count cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class IncreasedAst(Experiment):
    """Endpoint Information about increased AST for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The elevated AST liver enzyme levels, typically expressed in U/L")
    measured_dose: str = Field(None, description="The dose at which increased AST is observed")
    
    @model_validator(mode='after')
    def validate_model_type_for_increased_ast(self):
        """Validate that model_type is compatible with IncreasedAst endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for IncreasedAst: {self.model_type.value}. Increased AST cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class IncreasedAlt(Experiment):
    """Endpoint Information about increased ALT for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The elevated ALT liver enzyme levels, typically expressed in U/L")
    measured_dose: str = Field(None, description="The dose at which increased ALT is observed")
    
    @model_validator(mode='after')
    def validate_model_type_for_increased_alt(self):
        """Validate that model_type is compatible with IncreasedAlt endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for IncreasedAlt: {self.model_type.value}. Increased ALT cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class LungInflammation(Experiment):
    """Endpoint Information about lung inflammation for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The inflammatory response in lung tissue, typically described qualitatively or quantitatively")
    measured_dose: str = Field(None, description="The dose at which lung inflammation is observed")
    
    @model_validator(mode='after')
    def validate_model_type_for_lung_inflammation(self):
        """Validate that model_type is compatible with LungInflammation endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for LungInflammation: {self.model_type.value}. Lung inflammation cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class InterstitialLungDisease(Experiment):
    """Endpoint Information about interstitial lung disease for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The lung inflammation and fibrosis, typically described qualitatively or quantitatively")
    measured_dose: str = Field(None, description="The dose at which interstitial lung disease is observed")
    
    @model_validator(mode='after')
    def validate_model_type_for_interstitial_lung_disease(self):
        """Validate that model_type is compatible with InterstitialLungDisease endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for InterstitialLungDisease: {self.model_type.value}. Interstitial lung disease cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class TargetOrganIssues(Experiment):
    """Endpoint Information about target organ issues for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="The organ-specific toxicities caused by ADCs, typically described qualitatively")
    measured_dose: str = Field(None, description="The dose at which target organ issues are observed")
    affected_organs: List[str] = Field(default_factory=list, description="List of specific organs affected by toxicity")
    
    @model_validator(mode='after')
    def validate_model_type_for_target_organ_issues(self):
        """Validate that model_type is compatible with TargetOrganIssues endpoint."""
        allowed_model_types = {ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for TargetOrganIssues: {self.model_type.value}. Target organ issues cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

# Function to get the appropriate endpoint model class based on the endpoint name
def get_endpoint_model(endpoint_name: EndpointName) -> Type[Endpoint]:
    """Get the appropriate endpoint model class based on the endpoint name"""
    # Convert enum name to class name format (remove underscores and capitalize each word)
    # For example: ANTIGEN_EXPRESSION -> AntigenExpression
    class_name = ''.join(word.capitalize() for word in endpoint_name.split('_'))
    
    # Get the module where this function is defined
    module = sys.modules[__name__]
    
    # Try to get the class by name from the module
    try:
        return getattr(module, class_name)
    except AttributeError:
        # If the class doesn't exist, return the base Endpoint class
        return Endpoint
