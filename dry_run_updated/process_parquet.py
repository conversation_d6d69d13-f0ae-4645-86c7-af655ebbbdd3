import pandas as pd
import os
from pathlib import Path

# List of paper IDs you want to extract
paper_ids = [
    'W4284897854', 'W2314772071', 'W4226170358', 'W4400124600',
    'W4226097966', 'W4292181683', 'W2064424529', 'W4393115738'
]

# Read the parquet file
# Replace 'your_file.parquet' with the actual path to your parquet file
df = pd.read_parquet('your_file.parquet')

# First, let's inspect the dataframe structure
print("Column names in the parquet file:")
print(df.columns.tolist())
print(f"\nDataframe shape: {df.shape}")
print(f"\nFirst few rows:")
print(df.head())

# Pause here to check the output and identify correct column names
print("\n" + "="*50)
print("Please check the column names above and update the script accordingly.")
print("="*50)

Once you identify the correct column names, uncomment and modify the code below:

# Update these variable names based on the actual column names
paper_id_column = 'paper_id'  # Replace with actual column name
xml_content_column = 'raw_content'  # Replace with actual column name

# Create the output directory
output_dir = Path('xml_for_rerun')
output_dir.mkdir(exist_ok=True)

# Filter the dataframe for the specified paper IDs
filtered_df = df[df[paper_id_column].isin(paper_ids)]

# Extract and save XML files
extracted_count = 0
missing_ids = []

for _, row in filtered_df.iterrows():
    paper_id = row[paper_id_column]
    xml_content = row[xml_content_column]
    
    # Create filename
    filename = f"{paper_id}.xml"
    filepath = output_dir / filename
    
    # Write XML content to file
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(xml_content)
        extracted_count += 1
        print(f"Extracted: {filename}")
    except Exception as e:
        print(f"Error writing {filename}: {e}")

# Check for missing paper IDs
found_ids = set(filtered_df[paper_id_column].tolist())
requested_ids = set(paper_ids)
missing_ids = requested_ids - found_ids

# Summary
print(f"\n--- Summary ---")
print(f"Total requested: {len(paper_ids)}")
print(f"Successfully extracted: {extracted_count}")
print(f"Missing paper IDs: {len(missing_ids)}")

if missing_ids:
    print(f"Missing IDs: {', '.join(missing_ids)}")